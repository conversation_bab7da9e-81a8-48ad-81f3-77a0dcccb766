# Use this document to specify the minimum compute requirements.
# This document will be used to generate a list of recommended hardware for your subnet.

# This is intended to give a rough estimate of the minimum requirements
# so that the user can make an informed decision about whether or not
# they want to run a miner or validator on their machine.

# NOTE: Specification for miners may be different from validators

version: '1.0' # update this version key as needed, ideally should match your release version

compute_spec:

  miner:
    gpu:
      recommended_gpu: "NVIDIA H200"       # provide a recommended GPU to purchase/rent

    storage:
      min_space: 16           # Minimum free storage space (GB)
      recommended_space: 100  # Recommended free storage space (GB)
      type: "SSD"             # Preferred storage type (e.g., SSD, HDD)
      min_iops: 1000          # Minimum I/O operations per second (if applicable)
      recommended_iops: 5000  # Recommended I/O operations per second

    os:
      name: "Ubuntu"  # Name of the preferred operating system(s)
      version: 22.04  # Version of the preferred operating system(s)

  validator:
    gpu:
      recommended_gpu: "NVIDIA H200"       # provide a recommended GPU to purchase/rent

    storage:
      min_space: 128           # Minimum free storage space (GB)
      recommended_space: 256  # Recommended free storage space (GB)
      type: "SSD"             # Preferred storage type (e.g., SSD, HDD)
      min_iops: 1000          # Minimum I/O operations per second (if applicable)
      recommended_iops: 5000  # Recommended I/O operations per second

    os:
      name: "Ubuntu"  # Name of the preferred operating system(s)
      version: 22.04  # Version of the preferred operating system(s)

network_spec:
  bandwidth:
    download: 100  # Minimum download bandwidth (Mbps)
    upload: 20     # Minimum upload bandwidth (Mbps)
