"""
Comprehensive performance monitoring and metrics collection for the miner.
"""
import time
import threading
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import bittensor as bt


@dataclass
class SolveMetrics:
    """Metrics for a single circuit solve operation."""
    timestamp: float
    circuit_hash: str
    num_qubits: int
    num_gates: int
    complexity_score: float
    strategy_used: str
    solve_time: float
    success: bool
    cache_hit: bool
    memory_used_mb: float
    gpu_memory_used_mb: float = 0.0
    error_message: Optional[str] = None


@dataclass
class SystemMetrics:
    """System-level performance metrics."""
    timestamp: float
    cpu_usage_percent: float
    memory_usage_mb: float
    gpu_memory_usage_mb: float
    gpu_utilization_percent: float
    queue_size: int
    active_workers: int


class PerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, base_dir: Path, max_history: int = 10000):
        self.base_dir = Path(base_dir)
        self.metrics_dir = self.base_dir / "performance_metrics"
        self.metrics_dir.mkdir(exist_ok=True)
        
        self.max_history = max_history
        self.solve_history: deque = deque(maxlen=max_history)
        self.system_history: deque = deque(maxlen=max_history // 10)  # Less frequent system metrics
        
        self._lock = threading.Lock()
        self._monitoring_active = False
        self._monitor_thread: Optional[threading.Thread] = None
        
        # Aggregated statistics
        self.stats = {
            'total_solves': 0,
            'successful_solves': 0,
            'cache_hits': 0,
            'total_solve_time': 0.0,
            'strategy_usage': defaultdict(int),
            'strategy_success_rate': defaultdict(list),
            'qubit_size_distribution': defaultdict(int),
            'error_counts': defaultdict(int),
        }
        
        # Performance thresholds for alerts
        self.thresholds = {
            'max_solve_time': 60.0,  # seconds
            'min_success_rate': 0.8,
            'max_memory_usage': 16 * 1024,  # MB
            'max_queue_size': 100,
        }
        
        bt.logging.info(f"Performance monitor initialized, metrics dir: {self.metrics_dir}")
    
    def start_monitoring(self, interval: float = 30.0):
        """Start system monitoring thread."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_system,
            args=(interval,),
            daemon=True,
            name="PerformanceMonitor"
        )
        self._monitor_thread.start()
        bt.logging.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop system monitoring."""
        self._monitoring_active = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        bt.logging.info("Performance monitoring stopped")
    
    def record_solve(self, metrics: SolveMetrics):
        """Record metrics for a circuit solve operation."""
        with self._lock:
            self.solve_history.append(metrics)
            
            # Update aggregated stats
            self.stats['total_solves'] += 1
            if metrics.success:
                self.stats['successful_solves'] += 1
            if metrics.cache_hit:
                self.stats['cache_hits'] += 1
            
            self.stats['total_solve_time'] += metrics.solve_time
            self.stats['strategy_usage'][metrics.strategy_used] += 1
            self.stats['strategy_success_rate'][metrics.strategy_used].append(metrics.success)
            self.stats['qubit_size_distribution'][metrics.num_qubits] += 1
            
            if not metrics.success and metrics.error_message:
                self.stats['error_counts'][metrics.error_message] += 1
            
            # Check for performance alerts
            self._check_alerts(metrics)
            
            # Periodic persistence
            if self.stats['total_solves'] % 100 == 0:
                self._persist_metrics()
    
    def record_system_metrics(self, metrics: SystemMetrics):
        """Record system-level metrics."""
        with self._lock:
            self.system_history.append(metrics)
    
    def get_performance_summary(self, window_minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for the specified time window."""
        cutoff_time = time.time() - (window_minutes * 60)
        
        with self._lock:
            # Filter recent solve metrics
            recent_solves = [m for m in self.solve_history if m.timestamp >= cutoff_time]
            recent_system = [m for m in self.system_history if m.timestamp >= cutoff_time]
            
            if not recent_solves:
                return {'error': 'No data in specified time window'}
            
            # Calculate summary statistics
            total_solves = len(recent_solves)
            successful_solves = sum(1 for m in recent_solves if m.success)
            cache_hits = sum(1 for m in recent_solves if m.cache_hit)
            
            solve_times = [m.solve_time for m in recent_solves if m.success]
            avg_solve_time = sum(solve_times) / len(solve_times) if solve_times else 0
            
            # Strategy performance
            strategy_stats = defaultdict(lambda: {'count': 0, 'success': 0, 'avg_time': 0})
            for m in recent_solves:
                strategy_stats[m.strategy_used]['count'] += 1
                if m.success:
                    strategy_stats[m.strategy_used]['success'] += 1
                    strategy_stats[m.strategy_used]['avg_time'] += m.solve_time
            
            for strategy, stats in strategy_stats.items():
                if stats['success'] > 0:
                    stats['avg_time'] /= stats['success']
                stats['success_rate'] = stats['success'] / stats['count']
            
            # Qubit distribution
            qubit_dist = defaultdict(int)
            for m in recent_solves:
                qubit_dist[m.num_qubits] += 1
            
            # System metrics summary
            system_summary = {}
            if recent_system:
                system_summary = {
                    'avg_cpu_usage': sum(m.cpu_usage_percent for m in recent_system) / len(recent_system),
                    'avg_memory_usage_mb': sum(m.memory_usage_mb for m in recent_system) / len(recent_system),
                    'avg_gpu_memory_mb': sum(m.gpu_memory_usage_mb for m in recent_system) / len(recent_system),
                    'avg_queue_size': sum(m.queue_size for m in recent_system) / len(recent_system),
                    'avg_active_workers': sum(m.active_workers for m in recent_system) / len(recent_system),
                }
            
            return {
                'time_window_minutes': window_minutes,
                'total_solves': total_solves,
                'success_rate': successful_solves / total_solves,
                'cache_hit_rate': cache_hits / total_solves,
                'avg_solve_time_seconds': avg_solve_time,
                'strategy_performance': dict(strategy_stats),
                'qubit_distribution': dict(qubit_dist),
                'system_metrics': system_summary,
                'timestamp': time.time(),
            }
    
    def get_detailed_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics."""
        with self._lock:
            # Calculate success rates for strategies
            strategy_success_rates = {}
            for strategy, successes in self.stats['strategy_success_rate'].items():
                if successes:
                    strategy_success_rates[strategy] = sum(successes) / len(successes)
            
            return {
                'overall_stats': {
                    'total_solves': self.stats['total_solves'],
                    'success_rate': self.stats['successful_solves'] / max(self.stats['total_solves'], 1),
                    'cache_hit_rate': self.stats['cache_hits'] / max(self.stats['total_solves'], 1),
                    'avg_solve_time': self.stats['total_solve_time'] / max(self.stats['successful_solves'], 1),
                },
                'strategy_usage': dict(self.stats['strategy_usage']),
                'strategy_success_rates': strategy_success_rates,
                'qubit_distribution': dict(self.stats['qubit_size_distribution']),
                'error_counts': dict(self.stats['error_counts']),
                'history_size': len(self.solve_history),
                'system_history_size': len(self.system_history),
            }
    
    def _monitor_system(self, interval: float):
        """System monitoring loop."""
        while self._monitoring_active:
            try:
                metrics = self._collect_system_metrics()
                if metrics:
                    self.record_system_metrics(metrics)
            except Exception as e:
                bt.logging.debug(f"System metrics collection failed: {e}")
            
            time.sleep(interval)
    
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """Collect current system metrics."""
        try:
            import psutil
            
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_mb = memory.used / (1024 * 1024)
            
            # GPU metrics (if available)
            gpu_memory_mb = 0
            gpu_util = 0
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_memory_mb = torch.cuda.memory_allocated(0) / (1024 * 1024)
                    # GPU utilization would need nvidia-ml-py or similar
            except ImportError:
                pass
            
            return SystemMetrics(
                timestamp=time.time(),
                cpu_usage_percent=cpu_percent,
                memory_usage_mb=memory_mb,
                gpu_memory_usage_mb=gpu_memory_mb,
                gpu_utilization_percent=gpu_util,
                queue_size=0,  # Would need to be passed from solver worker
                active_workers=0,  # Would need to be passed from solver worker
            )
            
        except ImportError:
            # psutil not available
            return None
        except Exception as e:
            bt.logging.debug(f"System metrics collection error: {e}")
            return None
    
    def _check_alerts(self, metrics: SolveMetrics):
        """Check for performance alerts."""
        if metrics.solve_time > self.thresholds['max_solve_time']:
            bt.logging.warning(
                f"Slow solve detected: {metrics.solve_time:.2f}s for {metrics.num_qubits}-qubit circuit "
                f"using {metrics.strategy_used}"
            )
        
        if metrics.memory_used_mb > self.thresholds['max_memory_usage']:
            bt.logging.warning(
                f"High memory usage: {metrics.memory_used_mb:.1f}MB for {metrics.num_qubits}-qubit circuit"
            )
    
    def _persist_metrics(self):
        """Persist metrics to disk."""
        try:
            timestamp = int(time.time())
            
            # Save recent solve metrics
            solve_file = self.metrics_dir / f"solve_metrics_{timestamp}.json"
            recent_solves = list(self.solve_history)[-1000:]  # Last 1000 solves
            with open(solve_file, 'w') as f:
                json.dump([asdict(m) for m in recent_solves], f, indent=2)
            
            # Save aggregated stats
            stats_file = self.metrics_dir / f"stats_{timestamp}.json"
            stats_copy = dict(self.stats)
            # Convert defaultdicts to regular dicts for JSON serialization
            for key, value in stats_copy.items():
                if isinstance(value, defaultdict):
                    stats_copy[key] = dict(value)
            
            with open(stats_file, 'w') as f:
                json.dump(stats_copy, f, indent=2)
            
            bt.logging.debug(f"Metrics persisted to {self.metrics_dir}")
            
        except Exception as e:
            bt.logging.error(f"Failed to persist metrics: {e}")
    
    def export_metrics(self, output_file: Path):
        """Export all metrics to a file."""
        try:
            with self._lock:
                export_data = {
                    'solve_history': [asdict(m) for m in self.solve_history],
                    'system_history': [asdict(m) for m in self.system_history],
                    'aggregated_stats': dict(self.stats),
                    'export_timestamp': time.time(),
                }
            
            with open(output_file, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            bt.logging.info(f"Metrics exported to {output_file}")
            
        except Exception as e:
            bt.logging.error(f"Failed to export metrics: {e}")
    
    def clear_history(self):
        """Clear performance history."""
        with self._lock:
            self.solve_history.clear()
            self.system_history.clear()
            self.stats = {
                'total_solves': 0,
                'successful_solves': 0,
                'cache_hits': 0,
                'total_solve_time': 0.0,
                'strategy_usage': defaultdict(int),
                'strategy_success_rate': defaultdict(list),
                'qubit_size_distribution': defaultdict(int),
                'error_counts': defaultdict(int),
            }
        bt.logging.info("Performance history cleared")


# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor(base_dir: Path = None) -> PerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        if base_dir is None:
            base_dir = Path(__file__).parent.parent
        _performance_monitor = PerformanceMonitor(base_dir)
    return _performance_monitor
