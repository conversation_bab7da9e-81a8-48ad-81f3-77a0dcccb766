"""
Enhanced GPU memory management for quantum circuit simulation.
"""
import gc
import threading
import time
from typing import Optional, Dict, Any
import bittensor as bt


class GPUMemoryManager:
    """Thread-safe GPU memory manager with pooling and optimization."""
    
    def __init__(self):
        self._lock = threading.Lock()
        self._torch_available = False
        self._cuda_available = False
        self._memory_pool_enabled = False
        self._last_cleanup = 0
        self._cleanup_interval = 30  # seconds
        self._memory_stats = {
            'allocations': 0,
            'deallocations': 0,
            'peak_memory': 0,
            'current_memory': 0,
        }
        
        self._initialize_torch()
    
    def _initialize_torch(self):
        """Initialize PyTorch and CUDA if available."""
        try:
            import torch
            self._torch_available = True
            
            if torch.cuda.is_available():
                self._cuda_available = True
                self._setup_memory_pool()
                bt.logging.info(f"GPU memory manager initialized - CUDA devices: {torch.cuda.device_count()}")
                
                # Log initial GPU memory info
                for i in range(torch.cuda.device_count()):
                    props = torch.cuda.get_device_properties(i)
                    total_memory = props.total_memory / (1024**3)  # GB
                    bt.logging.info(f"GPU {i}: {props.name}, {total_memory:.1f}GB total memory")
            else:
                bt.logging.info("GPU memory manager initialized - CUDA not available")
                
        except ImportError:
            bt.logging.info("GPU memory manager initialized - PyTorch not available")
    
    def _setup_memory_pool(self):
        """Setup CUDA memory pool for better allocation performance."""
        try:
            import torch
            if self._cuda_available:
                # Enable memory pool
                torch.cuda.empty_cache()
                self._memory_pool_enabled = True
                bt.logging.info("CUDA memory pool enabled")
        except Exception as e:
            bt.logging.warning(f"Failed to setup memory pool: {e}")
    
    def allocate_for_circuit(self, num_qubits: int) -> bool:
        """
        Check if we can allocate memory for a circuit of given size.
        
        Args:
            num_qubits: Number of qubits in the circuit
            
        Returns:
            True if allocation is likely to succeed
        """
        if not self._cuda_available:
            return True  # CPU fallback always available
            
        with self._lock:
            try:
                import torch
                
                # Estimate memory requirement (rough approximation)
                # Statevector needs 2^n complex numbers, each 16 bytes (complex128)
                estimated_bytes = (2 ** num_qubits) * 16
                estimated_gb = estimated_bytes / (1024**3)
                
                # Get available memory
                available_memory = torch.cuda.get_device_properties(0).total_memory
                allocated_memory = torch.cuda.memory_allocated(0)
                free_memory = available_memory - allocated_memory
                
                # Keep 20% buffer
                usable_memory = free_memory * 0.8
                
                can_allocate = estimated_bytes < usable_memory
                
                if not can_allocate:
                    bt.logging.warning(
                        f"Insufficient GPU memory for {num_qubits} qubits: "
                        f"need {estimated_gb:.2f}GB, have {usable_memory/(1024**3):.2f}GB"
                    )
                
                return can_allocate
                
            except Exception as e:
                bt.logging.error(f"Memory allocation check failed: {e}")
                return False
    
    def cleanup_if_needed(self, force: bool = False):
        """Cleanup GPU memory if needed."""
        current_time = time.time()
        
        if not force and (current_time - self._last_cleanup) < self._cleanup_interval:
            return
            
        with self._lock:
            self._cleanup_memory()
            self._last_cleanup = current_time
    
    def _cleanup_memory(self):
        """Perform memory cleanup."""
        try:
            # Python garbage collection
            collected = gc.collect()
            
            if self._torch_available:
                import torch
                
                if self._cuda_available:
                    # Clear CUDA cache
                    torch.cuda.empty_cache()
                    
                    # Update memory stats
                    allocated = torch.cuda.memory_allocated(0)
                    self._memory_stats['current_memory'] = allocated
                    self._memory_stats['peak_memory'] = max(
                        self._memory_stats['peak_memory'], 
                        allocated
                    )
                    
                    bt.logging.debug(
                        f"Memory cleanup: collected {collected} objects, "
                        f"GPU memory: {allocated/(1024**2):.1f}MB"
                    )
                else:
                    bt.logging.debug(f"Memory cleanup: collected {collected} objects")
                    
        except Exception as e:
            bt.logging.error(f"Memory cleanup failed: {e}")
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory information."""
        info = {
            'torch_available': self._torch_available,
            'cuda_available': self._cuda_available,
            'memory_pool_enabled': self._memory_pool_enabled,
            'stats': self._memory_stats.copy()
        }
        
        if self._cuda_available:
            try:
                import torch
                info['gpu_memory'] = {
                    'allocated': torch.cuda.memory_allocated(0),
                    'cached': torch.cuda.memory_reserved(0),
                    'max_allocated': torch.cuda.max_memory_allocated(0),
                    'max_cached': torch.cuda.max_memory_reserved(0),
                }
            except Exception:
                pass
                
        return info
    
    def optimize_for_circuit_size(self, num_qubits: int):
        """Optimize memory settings for specific circuit size."""
        if not self._cuda_available:
            return
            
        try:
            import torch
            
            # For large circuits, be more aggressive with cleanup
            if num_qubits > 25:
                torch.cuda.empty_cache()
                
            # Adjust memory fraction based on circuit size
            if num_qubits > 30:
                # Reserve more memory for large circuits
                torch.cuda.set_per_process_memory_fraction(0.9)
            else:
                torch.cuda.set_per_process_memory_fraction(0.8)
                
        except Exception as e:
            bt.logging.debug(f"Memory optimization failed: {e}")
    
    def reset_peak_stats(self):
        """Reset peak memory statistics."""
        with self._lock:
            self._memory_stats['peak_memory'] = 0
            if self._cuda_available:
                try:
                    import torch
                    torch.cuda.reset_peak_memory_stats()
                except Exception:
                    pass


# Global memory manager instance
_memory_manager = GPUMemoryManager()

def get_memory_manager() -> GPUMemoryManager:
    """Get the global memory manager instance."""
    return _memory_manager
