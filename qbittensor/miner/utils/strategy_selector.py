"""
Advanced strategy selection for quantum circuit solving.
"""
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import bitten<PERSON> as bt

from .circuit_analyzer import CircuitCharacteristics
from .gpu_memory_manager import GPUMemoryManager


@dataclass
class SolverStrategy:
    """Represents a solving strategy with its parameters."""
    name: str
    method: str
    device: str
    shots: Optional[int] = None
    priority: int = 1
    estimated_time: float = 1.0
    memory_requirement: float = 0.1  # GB
    success_probability: float = 0.9


class StrategySelector:
    """Intelligent strategy selection based on circuit characteristics and system state."""
    
    def __init__(self, memory_manager: GPUMemoryManager):
        self.memory_manager = memory_manager
        self.strategy_history: Dict[str, List[Tuple[float, bool]]] = {}  # strategy -> [(time, success)]
        self.circuit_type_performance: Dict[str, Dict[str, float]] = {}  # circuit_type -> strategy -> avg_time
        
        # Define available strategies
        self.strategies = {
            "statevector_gpu_fast": SolverStrategy(
                name="statevector_gpu_fast",
                method="statevector",
                device="GPU",
                priority=1,
                estimated_time=0.5,
                memory_requirement=lambda n: (2**n) * 16 / (1024**3),
                success_probability=0.95
            ),
            "statevector_cpu_reliable": SolverStrategy(
                name="statevector_cpu_reliable",
                method="statevector",
                device="CPU",
                priority=2,
                estimated_time=2.0,
                memory_requirement=lambda n: (2**n) * 16 / (1024**3),
                success_probability=0.98
            ),
            "mps_efficient": SolverStrategy(
                name="mps_efficient",
                method="matrix_product_state",
                device="CPU",
                priority=3,
                estimated_time=5.0,
                memory_requirement=lambda n: min(8.0, n * 0.1),
                success_probability=0.85
            ),
            "sampling_scalable": SolverStrategy(
                name="sampling_scalable",
                method="automatic",
                device="CPU",
                shots=8192,
                priority=4,
                estimated_time=10.0,
                memory_requirement=lambda n: 1.0,  # Constant memory
                success_probability=0.75
            ),
            "sampling_high_precision": SolverStrategy(
                name="sampling_high_precision",
                method="automatic",
                device="CPU",
                shots=32768,
                priority=5,
                estimated_time=30.0,
                memory_requirement=lambda n: 1.0,
                success_probability=0.80
            )
        }
    
    def select_strategy(self, characteristics: CircuitCharacteristics) -> List[SolverStrategy]:
        """
        Select the best strategies for a given circuit, ordered by preference.
        
        Args:
            characteristics: Circuit characteristics from analysis
            
        Returns:
            List of strategies ordered by preference
        """
        num_qubits = characteristics.num_qubits
        complexity = characteristics.complexity_score
        
        # Filter strategies based on feasibility
        feasible_strategies = []
        
        for strategy in self.strategies.values():
            if self._is_strategy_feasible(strategy, characteristics):
                # Calculate dynamic priority based on current conditions
                dynamic_priority = self._calculate_dynamic_priority(strategy, characteristics)
                strategy_copy = SolverStrategy(
                    name=strategy.name,
                    method=strategy.method,
                    device=strategy.device,
                    shots=strategy.shots,
                    priority=dynamic_priority,
                    estimated_time=self._estimate_time(strategy, characteristics),
                    memory_requirement=self._calculate_memory_requirement(strategy, num_qubits),
                    success_probability=self._estimate_success_probability(strategy, characteristics)
                )
                feasible_strategies.append(strategy_copy)
        
        # Sort by priority (lower is better) and success probability
        feasible_strategies.sort(key=lambda s: (s.priority, -s.success_probability, s.estimated_time))
        
        bt.logging.info(f"Selected {len(feasible_strategies)} feasible strategies for {num_qubits}-qubit circuit")
        for i, strategy in enumerate(feasible_strategies[:3]):  # Log top 3
            bt.logging.debug(
                f"  {i+1}. {strategy.name}: priority={strategy.priority}, "
                f"time={strategy.estimated_time:.1f}s, success={strategy.success_probability:.2f}"
            )
        
        return feasible_strategies
    
    def _is_strategy_feasible(self, strategy: SolverStrategy, characteristics: CircuitCharacteristics) -> bool:
        """Check if a strategy is feasible for the given circuit."""
        num_qubits = characteristics.num_qubits
        
        # Memory check
        memory_req = self._calculate_memory_requirement(strategy, num_qubits)
        if strategy.device == "GPU":
            if not self.memory_manager.allocate_for_circuit(num_qubits):
                return False
        
        # Qubit limits for different methods
        if strategy.method == "statevector" and num_qubits > 35:
            return False  # Statevector becomes impractical
        
        if strategy.method == "matrix_product_state" and num_qubits > 100:
            return False  # Even MPS has limits
        
        # Device availability
        if strategy.device == "GPU" and not self.memory_manager._cuda_available:
            return False
        
        return True
    
    def _calculate_dynamic_priority(self, strategy: SolverStrategy, characteristics: CircuitCharacteristics) -> int:
        """Calculate dynamic priority based on current system state and circuit characteristics."""
        base_priority = strategy.priority
        
        # Adjust based on circuit size
        num_qubits = characteristics.num_qubits
        if num_qubits <= 20:
            # Small circuits: prefer GPU statevector
            if strategy.name == "statevector_gpu_fast":
                base_priority -= 2
        elif num_qubits <= 32:
            # Medium circuits: balance between GPU and CPU
            if strategy.name in ["statevector_gpu_fast", "statevector_cpu_reliable"]:
                base_priority -= 1
        else:
            # Large circuits: prefer MPS and sampling
            if strategy.name in ["mps_efficient", "sampling_scalable"]:
                base_priority -= 2
        
        # Adjust based on complexity
        if characteristics.complexity_score > 1e6:
            # High complexity: prefer more reliable methods
            if "reliable" in strategy.name or "efficient" in strategy.name:
                base_priority -= 1
        
        # Adjust based on historical performance
        if strategy.name in self.strategy_history:
            recent_performance = self._get_recent_performance(strategy.name)
            if recent_performance > 0.8:  # Good recent performance
                base_priority -= 1
            elif recent_performance < 0.5:  # Poor recent performance
                base_priority += 2
        
        return max(1, base_priority)  # Ensure priority is at least 1
    
    def _calculate_memory_requirement(self, strategy: SolverStrategy, num_qubits: int) -> float:
        """Calculate memory requirement for a strategy."""
        if callable(strategy.memory_requirement):
            return strategy.memory_requirement(num_qubits)
        return strategy.memory_requirement
    
    def _estimate_time(self, strategy: SolverStrategy, characteristics: CircuitCharacteristics) -> float:
        """Estimate execution time for a strategy."""
        base_time = strategy.estimated_time
        
        # Adjust based on circuit characteristics
        complexity_factor = min(10.0, characteristics.complexity_score / 1e5)
        depth_factor = min(5.0, characteristics.depth / 100)
        
        estimated_time = base_time * complexity_factor * depth_factor
        
        # Use historical data if available
        circuit_type = self._classify_circuit_type(characteristics)
        if circuit_type in self.circuit_type_performance:
            if strategy.name in self.circuit_type_performance[circuit_type]:
                historical_time = self.circuit_type_performance[circuit_type][strategy.name]
                # Blend historical and estimated time
                estimated_time = 0.7 * historical_time + 0.3 * estimated_time
        
        return estimated_time
    
    def _estimate_success_probability(self, strategy: SolverStrategy, characteristics: CircuitCharacteristics) -> float:
        """Estimate success probability for a strategy."""
        base_prob = strategy.success_probability
        
        # Adjust based on circuit size and complexity
        if characteristics.num_qubits > 30:
            if strategy.method == "statevector":
                base_prob *= 0.8  # Lower success for large statevector
        
        if characteristics.complexity_score > 1e7:
            base_prob *= 0.9  # Lower success for very complex circuits
        
        # Use historical data
        if strategy.name in self.strategy_history:
            recent_success_rate = self._get_recent_success_rate(strategy.name)
            # Blend historical and estimated probability
            base_prob = 0.6 * recent_success_rate + 0.4 * base_prob
        
        return max(0.1, min(1.0, base_prob))
    
    def _classify_circuit_type(self, characteristics: CircuitCharacteristics) -> str:
        """Classify circuit type for performance tracking."""
        num_qubits = characteristics.num_qubits
        complexity = characteristics.complexity_score
        
        if num_qubits <= 20:
            return "small"
        elif num_qubits <= 32:
            if complexity < 1e6:
                return "medium_simple"
            else:
                return "medium_complex"
        else:
            if complexity < 1e7:
                return "large_simple"
            else:
                return "large_complex"
    
    def record_performance(self, strategy_name: str, execution_time: float, success: bool, characteristics: CircuitCharacteristics):
        """Record performance data for a strategy."""
        # Record in strategy history
        if strategy_name not in self.strategy_history:
            self.strategy_history[strategy_name] = []
        
        self.strategy_history[strategy_name].append((execution_time, success))
        
        # Keep only recent history (last 100 entries)
        if len(self.strategy_history[strategy_name]) > 100:
            self.strategy_history[strategy_name] = self.strategy_history[strategy_name][-100:]
        
        # Record in circuit type performance
        circuit_type = self._classify_circuit_type(characteristics)
        if circuit_type not in self.circuit_type_performance:
            self.circuit_type_performance[circuit_type] = {}
        
        if strategy_name not in self.circuit_type_performance[circuit_type]:
            self.circuit_type_performance[circuit_type][strategy_name] = execution_time
        else:
            # Exponential moving average
            current_avg = self.circuit_type_performance[circuit_type][strategy_name]
            self.circuit_type_performance[circuit_type][strategy_name] = 0.8 * current_avg + 0.2 * execution_time
        
        bt.logging.debug(f"Recorded performance: {strategy_name} took {execution_time:.2f}s, success={success}")
    
    def _get_recent_performance(self, strategy_name: str, window: int = 20) -> float:
        """Get recent performance score (success rate) for a strategy."""
        if strategy_name not in self.strategy_history:
            return 0.5  # Default
        
        recent_entries = self.strategy_history[strategy_name][-window:]
        if not recent_entries:
            return 0.5
        
        success_count = sum(1 for _, success in recent_entries if success)
        return success_count / len(recent_entries)
    
    def _get_recent_success_rate(self, strategy_name: str, window: int = 20) -> float:
        """Get recent success rate for a strategy."""
        return self._get_recent_performance(strategy_name, window)
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics for all strategies."""
        stats = {}
        for strategy_name, history in self.strategy_history.items():
            if history:
                times = [t for t, _ in history]
                successes = [s for _, s in history]
                stats[strategy_name] = {
                    'total_attempts': len(history),
                    'success_rate': sum(successes) / len(successes),
                    'avg_time': sum(times) / len(times),
                    'recent_success_rate': self._get_recent_success_rate(strategy_name)
                }
        return stats
