"""
Circuit analysis and preprocessing utilities.
"""
import hashlib
import re
import threading
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
import bittensor as bt


@dataclass
class CircuitCharacteristics:
    """Characteristics of a quantum circuit."""
    num_qubits: int
    num_gates: int
    depth: int
    gate_types: Dict[str, int]
    has_measurements: bool
    complexity_score: float
    recommended_method: str
    estimated_memory_gb: float


class CircuitAnalyzer:
    """Analyzes quantum circuits to determine optimal solving strategies."""
    
    def __init__(self):
        self.gate_complexity = {
            'h': 1, 'x': 1, 'y': 1, 'z': 1,
            'rx': 2, 'ry': 2, 'rz': 2,
            'cx': 2, 'cy': 2, 'cz': 2,
            'ccx': 4, 'ccy': 4, 'ccz': 4,
            'rxx': 3, 'ryy': 3, 'rzz': 3,
            'u1': 2, 'u2': 3, 'u3': 4,
        }
    
    def analyze(self, qasm: str) -> CircuitCharacteristics:
        """
        Analyze a QASM circuit and return its characteristics.
        
        Args:
            qasm: QASM string representation of the circuit
            
        Returns:
            CircuitCharacteristics object with analysis results
        """
        try:
            num_qubits = self._count_qubits(qasm)
            gates_info = self._analyze_gates(qasm)
            depth = self._estimate_depth(qasm)
            has_measurements = 'measure' in qasm.lower()
            
            complexity_score = self._calculate_complexity(num_qubits, gates_info, depth)
            recommended_method = self._recommend_method(num_qubits, complexity_score)
            estimated_memory = self._estimate_memory_requirement(num_qubits)
            
            return CircuitCharacteristics(
                num_qubits=num_qubits,
                num_gates=gates_info['total'],
                depth=depth,
                gate_types=gates_info['types'],
                has_measurements=has_measurements,
                complexity_score=complexity_score,
                recommended_method=recommended_method,
                estimated_memory_gb=estimated_memory
            )
            
        except Exception as e:
            bt.logging.error(f"Circuit analysis failed: {e}")
            # Return default characteristics
            return CircuitCharacteristics(
                num_qubits=1,
                num_gates=0,
                depth=0,
                gate_types={},
                has_measurements=False,
                complexity_score=0.0,
                recommended_method="statevector",
                estimated_memory_gb=0.001
            )
    
    def _count_qubits(self, qasm: str) -> int:
        """Count the number of qubits in the circuit."""
        for line in qasm.split('\n'):
            line = line.strip()
            if line.startswith('qreg'):
                match = re.search(r'qreg\s+\w+\[(\d+)\]', line)
                if match:
                    return int(match.group(1))
        return 1
    
    def _analyze_gates(self, qasm: str) -> Dict[str, Any]:
        """Analyze gates in the circuit."""
        gate_types = {}
        total_gates = 0
        
        lines = qasm.split('\n')
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//') or line.startswith('OPENQASM') or \
               line.startswith('include') or line.startswith('qreg') or line.startswith('creg'):
                continue
                
            # Extract gate name
            parts = line.split()
            if parts:
                gate_name = parts[0].lower()
                if gate_name in self.gate_complexity or gate_name in ['measure']:
                    gate_types[gate_name] = gate_types.get(gate_name, 0) + 1
                    total_gates += 1
        
        return {
            'types': gate_types,
            'total': total_gates
        }
    
    def _estimate_depth(self, qasm: str) -> int:
        """Estimate circuit depth (rough approximation)."""
        lines = [line.strip() for line in qasm.split('\n') 
                if line.strip() and not line.strip().startswith('//') 
                and not line.strip().startswith('OPENQASM')
                and not line.strip().startswith('include')
                and not line.strip().startswith('qreg')
                and not line.strip().startswith('creg')]
        
        # Simple approximation: count non-declaration lines
        gate_lines = [line for line in lines if line and not line.startswith('//')]
        return len(gate_lines)
    
    def _calculate_complexity(self, num_qubits: int, gates_info: Dict, depth: int) -> float:
        """Calculate a complexity score for the circuit."""
        base_complexity = 2 ** min(num_qubits, 20)  # Cap exponential growth
        
        gate_complexity = 0
        for gate_type, count in gates_info['types'].items():
            complexity_factor = self.gate_complexity.get(gate_type, 2)
            gate_complexity += count * complexity_factor
        
        depth_factor = max(1, depth / 10)  # Depth contributes to complexity
        
        return base_complexity * gate_complexity * depth_factor
    
    def _recommend_method(self, num_qubits: int, complexity_score: float) -> str:
        """Recommend the best simulation method based on circuit characteristics."""
        if num_qubits <= 20:
            return "statevector_gpu"
        elif num_qubits <= 32:
            if complexity_score < 1e6:
                return "statevector_gpu"
            else:
                return "statevector_cpu"
        elif num_qubits <= 50:
            return "matrix_product_state"
        else:
            return "sampling"
    
    def _estimate_memory_requirement(self, num_qubits: int) -> float:
        """Estimate memory requirement in GB."""
        # Statevector: 2^n complex numbers, each 16 bytes (complex128)
        if num_qubits <= 32:
            bytes_required = (2 ** num_qubits) * 16
            return bytes_required / (1024**3)  # Convert to GB
        else:
            # MPS and other methods have lower memory requirements
            return min(8.0, num_qubits * 0.1)  # Rough approximation
    
    def get_circuit_hash(self, qasm: str) -> str:
        """Generate a hash for the circuit for caching purposes."""
        # Normalize the QASM by removing comments and extra whitespace
        normalized = self._normalize_qasm(qasm)
        return hashlib.sha256(normalized.encode()).hexdigest()
    
    def _normalize_qasm(self, qasm: str) -> str:
        """Normalize QASM for consistent hashing."""
        lines = []
        for line in qasm.split('\n'):
            line = line.strip()
            if line and not line.startswith('//'):
                lines.append(line)
        return '\n'.join(sorted(lines))  # Sort for consistency


class CircuitCache:
    """Thread-safe cache for circuit solutions."""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: Dict[str, Tuple[str, float]] = {}  # hash -> (solution, timestamp)
        self.access_times: Dict[str, float] = {}
        self._lock = threading.Lock()
        
    def get(self, circuit_hash: str) -> Optional[str]:
        """Get cached solution for a circuit."""
        import time
        
        with self._lock:
            if circuit_hash in self.cache:
                solution, _ = self.cache[circuit_hash]
                self.access_times[circuit_hash] = time.time()
                bt.logging.debug(f"Cache hit for circuit {circuit_hash[:10]}")
                return solution
            return None
    
    def put(self, circuit_hash: str, solution: str):
        """Cache a solution for a circuit."""
        import time
        
        with self._lock:
            current_time = time.time()
            
            # Remove oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[circuit_hash] = (solution, current_time)
            self.access_times[circuit_hash] = current_time
            bt.logging.debug(f"Cached solution for circuit {circuit_hash[:10]}")
    
    def _evict_oldest(self):
        """Evict the least recently used entry."""
        if not self.access_times:
            return
            
        oldest_hash = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[oldest_hash]
        del self.access_times[oldest_hash]
        bt.logging.debug(f"Evicted cached solution for circuit {oldest_hash[:10]}")
    
    def clear(self):
        """Clear the cache."""
        with self._lock:
            self.cache.clear()
            self.access_times.clear()
            bt.logging.info("Circuit cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': getattr(self, '_hit_rate', 0.0)
            }


# Global instances
_analyzer = CircuitAnalyzer()
_cache = CircuitCache()

def get_circuit_analyzer() -> CircuitAnalyzer:
    """Get the global circuit analyzer instance."""
    return _analyzer

def get_circuit_cache() -> CircuitCache:
    """Get the global circuit cache instance."""
    return _cache
