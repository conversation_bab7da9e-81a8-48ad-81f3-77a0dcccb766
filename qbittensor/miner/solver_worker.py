from __future__ import annotations

"""
Enhanced multi-threaded worker for the miner
"""

import asyncio
import threading
import time
from collections.abc import Callable
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Tuple, Optional
import multiprocessing

import bittensor as bt

from qbittensor.protocol import ChallengeCircuits

from .config import DEFAULT_QUEUE_SIZE, DEFAULT_SCAN_INTERVAL, Paths
from .extract import cid_from_filename, qasm_from_file, qasm_from_synapse
from .storage import Storage

__all__ = ["SolverWorker"]


class SolverWorker:
    """Enhanced multi-threaded worker that pulls work from two sources → solves concurrently → hands results to *Storage*."""

    def __init__(
        self,
        base_dir: Path,
        *,
        solver_fn: Callable[[str], str],
        queue_size: int = DEFAULT_QUEUE_SIZE,
        scan_interval: float = DEFAULT_SCAN_INTERVAL,
        thread_name: str = "CircuitSolver",
        max_workers: Optional[int] = None,
        batch_size: int = 5,
    ) -> None:
        self.paths = Paths.from_base(base_dir)
        self.storage = Storage(self.paths)
        self._solve = solver_fn
        self._scan_interval = scan_interval
        self._queue: "asyncio.Queue[Tuple[str, str, str]]" = asyncio.Queue(
            maxsize=queue_size
        )  # (cid, qasm, validator_hotkey)
        self._thread_name = thread_name
        self._running = False

        # Multi-threading configuration
        self._max_workers = max_workers or min(multiprocessing.cpu_count(), 8)
        self._batch_size = batch_size
        self._executor: Optional[ThreadPoolExecutor] = None
        self._active_tasks = set()

        # Performance metrics
        self._stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'concurrent_peak': 0,
            'queue_peak': 0,
        }

        bt.logging.info(f"SolverWorker configured with {self._max_workers} workers, batch size {self._batch_size}")

    def start(self) -> None:
        if self._running:
            return
        self._executor = ThreadPoolExecutor(max_workers=self._max_workers, thread_name_prefix="CircuitSolver")
        self._start_thread()

    def submit_synapse(self, syn: ChallengeCircuits) -> None:
        """Extract QASM & CID then push to queue (idempotent)."""
        cid = syn.challenge_id
        if self.storage.is_solved(cid):
            return
        if qasm := qasm_from_synapse(syn):
            validator_hotkey = getattr(syn, "validator_hotkey", None) or ""
            self._enqueue(cid, qasm, validator_hotkey)

    def submit_file(self, fp: Path) -> None:
        cid = cid_from_filename(fp)
        if self.storage.is_solved(cid):
            return
        if qasm := qasm_from_file(fp):
            self._enqueue(cid, qasm, "")

    def drain_solutions(self, *, n: int = 10, validator_hotkey: str = None):
        return self.storage.drain_unsent(max_count=n, validator_hotkey=validator_hotkey)

    def record_reward(self, cid: str, certificate: dict) -> None:
        self.storage.save_certificate(cid, certificate)

    def get_stats(self) -> dict:
        """Get performance statistics."""
        stats = self._stats.copy()
        stats['queue_size'] = self._queue.qsize()
        stats['active_tasks'] = len(self._active_tasks)
        stats['avg_time_per_circuit'] = stats['total_time'] / max(stats['total_processed'], 1)
        return stats

    def stop(self) -> None:
        """Gracefully stop the worker."""
        self._running = False
        if self._executor:
            bt.logging.info("Shutting down solver worker...")
            self._executor.shutdown(wait=True)
            bt.logging.info("Solver worker stopped")

    # Internal helpers
    # Queue mgmt

    def _enqueue(self, cid: str, qasm: str, validator_hotkey: str = ""):
        try:
            self._queue.put_nowait((cid, qasm, validator_hotkey))
            bt.logging.debug(
                f" queued {cid[:10]} from validator {validator_hotkey[:10] if validator_hotkey else 'unknown'}"
            )
        except asyncio.QueueFull:
            bt.logging.warning(" solver queue full. dropping challenge")

    # Thread bootstrap

    def _start_thread(self):
        def runner():
            self._running = True
            try:
                asyncio.run(self._main_loop())
            finally:
                self._running = False
                if self._executor:
                    self._executor.shutdown(wait=False)

        threading.Thread(target=runner, name=self._thread_name, daemon=True).start()
        bt.logging.info(f" Enhanced solver thread '{self._thread_name}' started with {self._max_workers} workers")

    # Enhanced concurrent main loop

    async def _main_loop(self):
        self._scan_unsolved_dir()  # once on start‑up

        while self._running:
            # Collect a batch of tasks
            batch = []
            batch_timeout = self._scan_interval / self._batch_size

            for _ in range(self._batch_size):
                try:
                    task = await asyncio.wait_for(self._queue.get(), timeout=batch_timeout)
                    batch.append(task)
                    self._stats['queue_peak'] = max(self._stats['queue_peak'], self._queue.qsize())
                except asyncio.TimeoutError:
                    break

            if not batch:
                self._scan_unsolved_dir()
                continue

            # Process batch concurrently
            await self._process_batch_concurrent(batch)

            # Mark tasks as done
            for _ in batch:
                self._queue.task_done()

    async def _process_batch_concurrent(self, batch):
        """Process a batch of circuits concurrently using thread pool."""
        if not batch:
            return

        # Submit all tasks to thread pool
        loop = asyncio.get_event_loop()
        futures = []

        for cid, qasm, validator_hotkey in batch:
            future = loop.run_in_executor(
                self._executor,
                self._solve_with_timing,
                cid, qasm, validator_hotkey
            )
            futures.append((future, cid, validator_hotkey))
            self._active_tasks.add(cid)

        self._stats['concurrent_peak'] = max(self._stats['concurrent_peak'], len(futures))

        # Wait for all tasks to complete
        for future, cid, validator_hotkey in futures:
            try:
                bits, solve_time = await future
                bt.logging.debug(f" {cid[:10]} → {bits} (took {solve_time:.2f}s)")
                self.storage.save_solution(cid, bits, validator_hotkey or None)
                self._stats['total_processed'] += 1
                self._stats['total_time'] += solve_time
            except Exception as e:
                bt.logging.error(f"Failed to solve circuit {cid[:10]}: {e}")
            finally:
                self._active_tasks.discard(cid)

    def _solve_with_timing(self, cid: str, qasm: str, validator_hotkey: str) -> Tuple[str, float]:
        """Solve circuit with timing measurement."""
        start_time = time.time()

        bt.logging.debug(
            f" solving {cid[:10]} from validator {validator_hotkey[:10] if validator_hotkey else 'unknown'}"
        )

        try:
            bits = self._solve(qasm)
            solve_time = time.time() - start_time
            return bits, solve_time
        except Exception as e:
            solve_time = time.time() - start_time
            bt.logging.error(f"Circuit solving failed for {cid[:10]}: {e}")
            return "", solve_time

    # File‑system polling

    def _scan_unsolved_dir(self):
        for fp in self.paths.unsolved.glob("*"):
            self.submit_file(fp)
