"""
Enhanced storage system with batch processing and optimized I/O operations.
"""
import json
import time
import threading
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from collections import deque
from dataclasses import dataclass
import bittensor as bt

from .config import Paths


@dataclass
class PendingWrite:
    """Represents a pending write operation."""
    file_path: Path
    data: dict
    timestamp: float
    priority: int = 1  # Lower is higher priority


class EnhancedStorage:
    """Enhanced storage with batch processing, caching, and optimized I/O."""
    
    def __init__(self, paths: Paths, batch_size: int = 50, flush_interval: float = 5.0):
        self.p = paths
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        # In-memory caches
        self._solved: Dict[str, str] = {}
        self._challenge_validators: Dict[str, str] = {}
        self._sent: Set[str] = set()
        self._certificates: Dict[str, dict] = {}
        
        # Batch processing
        self._write_queue: deque = deque()
        self._write_lock = threading.Lock()
        self._last_flush = time.time()
        self._flush_thread: Optional[threading.Thread] = None
        self._running = True
        
        # Performance metrics
        self._stats = {
            'reads': 0,
            'writes': 0,
            'batch_writes': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'flush_operations': 0,
        }
        
        self._bootstrap()
        self._start_flush_thread()
        
        bt.logging.info(f"Enhanced storage initialized with batch size {batch_size}, flush interval {flush_interval}s")
    
    def _bootstrap(self) -> None:
        """Bootstrap storage by loading existing files efficiently."""
        start_time = time.time()
        
        # Load solutions in batch
        solution_files = list(self.p.solved.glob("*.json"))
        self._batch_load_files(solution_files, self._load_solution_file)
        
        # Load certificates if directory exists
        cert_dir = self.p.base / "certificates"
        if cert_dir.exists():
            cert_files = list(cert_dir.glob("*.json"))
            self._batch_load_files(cert_files, self._load_certificate_file)
        
        load_time = time.time() - start_time
        bt.logging.info(
            f"Storage bootstrap completed in {load_time:.2f}s: "
            f"{len(self._solved)} solutions, {len(self._certificates)} certificates"
        )
    
    def _batch_load_files(self, file_paths: List[Path], load_func):
        """Load files in batches for better performance."""
        batch_size = 100
        for i in range(0, len(file_paths), batch_size):
            batch = file_paths[i:i + batch_size]
            for file_path in batch:
                try:
                    load_func(file_path)
                    self._stats['reads'] += 1
                except Exception as e:
                    bt.logging.debug(f"Failed to load {file_path.name}: {e}")
    
    def _load_solution_file(self, file_path: Path):
        """Load a single solution file."""
        meta = json.loads(file_path.read_text())
        cid, bits = meta["challenge_id"], meta["peak_bitstring"]
        self._solved[cid] = bits
        
        if "validator_hotkey" in meta:
            self._challenge_validators[cid] = meta["validator_hotkey"]
    
    def _load_certificate_file(self, file_path: Path):
        """Load a single certificate file."""
        cert_data = json.loads(file_path.read_text())
        if "challenge_id" in cert_data:
            self._certificates[cert_data["challenge_id"]] = cert_data
    
    def _start_flush_thread(self):
        """Start the background flush thread."""
        self._flush_thread = threading.Thread(
            target=self._flush_loop,
            daemon=True,
            name="StorageFlush"
        )
        self._flush_thread.start()
    
    def _flush_loop(self):
        """Background thread for flushing pending writes."""
        while self._running:
            try:
                current_time = time.time()
                should_flush = (
                    len(self._write_queue) >= self.batch_size or
                    (self._write_queue and current_time - self._last_flush >= self.flush_interval)
                )
                
                if should_flush:
                    self._flush_pending_writes()
                
                time.sleep(1.0)  # Check every second
                
            except Exception as e:
                bt.logging.error(f"Flush loop error: {e}")
    
    def _flush_pending_writes(self):
        """Flush pending writes to disk."""
        if not self._write_queue:
            return
        
        with self._write_lock:
            writes_to_process = []
            while self._write_queue and len(writes_to_process) < self.batch_size:
                writes_to_process.append(self._write_queue.popleft())
        
        if not writes_to_process:
            return
        
        # Group writes by directory for better I/O performance
        writes_by_dir = {}
        for write in writes_to_process:
            dir_path = write.file_path.parent
            if dir_path not in writes_by_dir:
                writes_by_dir[dir_path] = []
            writes_by_dir[dir_path].append(write)
        
        # Process writes directory by directory
        total_written = 0
        for dir_path, dir_writes in writes_by_dir.items():
            dir_path.mkdir(parents=True, exist_ok=True)
            
            for write in dir_writes:
                try:
                    write.file_path.write_text(json.dumps(write.data, separators=(',', ':')))
                    total_written += 1
                except Exception as e:
                    bt.logging.error(f"Failed to write {write.file_path}: {e}")
        
        self._stats['batch_writes'] += 1
        self._stats['writes'] += total_written
        self._stats['flush_operations'] += 1
        self._last_flush = time.time()
        
        bt.logging.debug(f"Flushed {total_written} files in batch")
    
    def save_solution(self, cid: str, bitstring: str, validator_hotkey: str = None) -> None:
        """Save solution with batched I/O."""
        # Update in-memory cache immediately
        self._solved[cid] = bitstring
        if validator_hotkey:
            self._challenge_validators[cid] = validator_hotkey
        
        # Prepare data for batched write
        payload = {
            "challenge_id": cid,
            "peak_bitstring": bitstring,
            "timestamp": time.time(),
        }
        if validator_hotkey:
            payload["validator_hotkey"] = validator_hotkey
        
        # Queue for batched write
        write_op = PendingWrite(
            file_path=self.p.solved / f"{cid}.json",
            data=payload,
            timestamp=time.time(),
            priority=1
        )
        
        with self._write_lock:
            self._write_queue.append(write_op)
        
        # Clean up unsolved files (immediate operation for correctness)
        self._cleanup_unsolved_files(cid)
    
    def _cleanup_unsolved_files(self, cid: str):
        """Clean up unsolved files for a given challenge ID."""
        try:
            for fp in self.p.unsolved.glob(f"{cid}*"):
                fp.unlink(missing_ok=True)
        except Exception as e:
            bt.logging.debug(f"Failed to cleanup unsolved files for {cid}: {e}")
    
    def save_certificate(self, cid: str, certificate: dict) -> None:
        """Save certificate with batched I/O."""
        # Update in-memory cache
        self._certificates[cid] = certificate
        
        # Queue for batched write
        cert_dir = self.p.base / "certificates"
        validator_hotkey = certificate.get("validator_hotkey", "unknown")
        
        write_op = PendingWrite(
            file_path=cert_dir / f"{cid}__{validator_hotkey}.json",
            data=certificate,
            timestamp=time.time(),
            priority=2  # Lower priority than solutions
        )
        
        with self._write_lock:
            self._write_queue.append(write_op)
    
    def is_solved(self, cid: str) -> bool:
        """Check if a challenge is already solved."""
        return cid in self._solved
    
    def get_solution(self, cid: str) -> Optional[str]:
        """Get solution for a challenge ID."""
        self._stats['cache_hits' if cid in self._solved else 'cache_misses'] += 1
        return self._solved.get(cid)
    
    def drain_unsent(self, max_count: int = 10, validator_hotkey: str = None) -> List[Tuple[str, str]]:
        """Return unsent solutions for the specified validator."""
        output: List[Tuple[str, str]] = []
        
        bt.logging.debug(
            f"[storage] drain_unsent: validator_hotkey={validator_hotkey}, "
            f"solved={len(self._solved)}, sent={len(self._sent)}"
        )
        
        for cid, bitstring in self._solved.items():
            if len(output) >= max_count:
                break
            
            # Skip if already sent
            if cid in self._sent:
                continue
            
            # Skip if caller asked for a specific validator and this CID belongs to another
            if validator_hotkey and self._challenge_validators.get(cid) != validator_hotkey:
                continue
            
            output.append((cid, bitstring))
            self._sent.add(cid)
        
        return output
    
    def get_certificates(self, max_count: int = 32) -> List[dict]:
        """Get certificates for gossiping."""
        certificates = list(self._certificates.values())
        return certificates[:max_count]
    
    def force_flush(self):
        """Force immediate flush of all pending writes."""
        bt.logging.info("Forcing storage flush...")
        self._flush_pending_writes()
    
    def get_stats(self) -> dict:
        """Get storage performance statistics."""
        with self._write_lock:
            queue_size = len(self._write_queue)
        
        stats = self._stats.copy()
        stats.update({
            'solved_count': len(self._solved),
            'certificates_count': len(self._certificates),
            'sent_count': len(self._sent),
            'pending_writes': queue_size,
            'cache_hit_rate': stats['cache_hits'] / max(stats['cache_hits'] + stats['cache_misses'], 1),
        })
        
        return stats
    
    def optimize_for_workload(self, expected_load: str = "medium"):
        """Optimize storage settings for expected workload."""
        if expected_load == "high":
            self.batch_size = min(100, self.batch_size * 2)
            self.flush_interval = max(2.0, self.flush_interval / 2)
        elif expected_load == "low":
            self.batch_size = max(10, self.batch_size // 2)
            self.flush_interval = min(10.0, self.flush_interval * 2)
        
        bt.logging.info(f"Storage optimized for {expected_load} load: batch_size={self.batch_size}, flush_interval={self.flush_interval}")
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """Clean up old files to free disk space."""
        cutoff_time = time.time() - (max_age_hours * 3600)
        cleaned_count = 0
        
        # Clean old solution files
        for file_path in self.p.solved.glob("*.json"):
            try:
                if file_path.stat().st_mtime < cutoff_time:
                    # Check if still in memory cache
                    try:
                        data = json.loads(file_path.read_text())
                        cid = data.get("challenge_id")
                        if cid and cid not in self._solved:
                            file_path.unlink()
                            cleaned_count += 1
                    except:
                        pass
            except Exception as e:
                bt.logging.debug(f"Error cleaning {file_path}: {e}")
        
        if cleaned_count > 0:
            bt.logging.info(f"Cleaned up {cleaned_count} old files")
    
    def shutdown(self):
        """Gracefully shutdown storage system."""
        bt.logging.info("Shutting down enhanced storage...")
        self._running = False
        
        # Force final flush
        self.force_flush()
        
        # Wait for flush thread to finish
        if self._flush_thread:
            self._flush_thread.join(timeout=10.0)
        
        bt.logging.info("Enhanced storage shutdown complete")
