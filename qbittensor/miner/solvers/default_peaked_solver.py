import gc
import time

import bitten<PERSON> as bt

from ..simulator import create_simulator
from ..task_processors import PeakedCircuitProcessor
from ..utils import get_memory_manager, get_circuit_analyzer, get_circuit_cache


class DefaultPeakedSolver:
    def __init__(self):
        self.memory_manager = get_memory_manager()
        self.circuit_analyzer = get_circuit_analyzer()
        self.circuit_cache = get_circuit_cache()

    def solve(self, qasm: str) -> str:
        """
        Solve a quantum circuit to find the peaked bitstring.

        Enhanced strategy with caching, analysis, and memory management:
        - Check cache first for previously solved circuits
        - Analyze circuit characteristics to choose optimal method
        - ≤32 qubits: GPU-accelerated statevector (if memory allows)
        - >32 qubits: Try MPS first, fallback to CPU statevector
        - Intelligent memory management and cleanup

        Args:
            qasm: QASM string of the circuit

        Returns:
            Most probable bitstring, or empty string if failed
        """
        start_time = time.time()

        try:
            # Check cache first
            circuit_hash = self.circuit_analyzer.get_circuit_hash(qasm)
            cached_result = self.circuit_cache.get(circuit_hash)
            if cached_result:
                bt.logging.info(f"Using cached solution for circuit {circuit_hash[:10]}")
                return cached_result

            # Analyze circuit characteristics
            characteristics = self.circuit_analyzer.analyze(qasm)
            num_qubits = characteristics.num_qubits

            bt.logging.info(
                f"Solving circuit: {num_qubits} qubits, {characteristics.num_gates} gates, "
                f"depth {characteristics.depth}, complexity {characteristics.complexity_score:.2e}, "
                f"recommended method: {characteristics.recommended_method}"
            )

            # Optimize memory for this circuit size
            self.memory_manager.optimize_for_circuit_size(num_qubits)

            # Choose solving strategy based on analysis
            result = self._solve_with_strategy(qasm, characteristics)

            # Cache the result if successful
            if result:
                self.circuit_cache.put(circuit_hash, result)
                solve_time = time.time() - start_time
                bt.logging.info(f"Circuit solution completed in {solve_time:.2f}s")

            return result

        except Exception as e:
            bt.logging.error(f"Circuit solving failed: {e}")
            self._clear_memory()
            return ""
        finally:
            # Cleanup memory after solving
            self.memory_manager.cleanup_if_needed()

    def _solve_with_strategy(self, qasm: str, characteristics) -> str:
        """Solve circuit using the recommended strategy."""
        method = characteristics.recommended_method
        num_qubits = characteristics.num_qubits

        # Check memory availability
        if not self.memory_manager.allocate_for_circuit(num_qubits):
            bt.logging.warning(f"Insufficient memory for {num_qubits} qubits, forcing MPS")
            method = "matrix_product_state"

        if method == "statevector_gpu":
            return self._run_statevector(qasm, prefer_gpu=True)
        elif method == "statevector_cpu":
            return self._run_statevector(qasm, prefer_gpu=False)
        elif method == "matrix_product_state":
            result = self._mps_run(qasm)
            if result:
                return result
            # Fallback to CPU statevector
            bt.logging.info("MPS failed, falling back to CPU statevector")
            return self._run_statevector(qasm, prefer_gpu=False)
        elif method == "sampling":
            # For very large circuits, use sampling approach
            return self._sampling_run(qasm)
        else:
            # Default fallback
            return self._run(qasm)

    def _mps_run(self, qasm: str) -> str:
        try:
            sim = create_simulator("qiskit", method="matrix_product_state", device="CPU")
            bt.logging.debug(f"Using MPS simulation on device: {sim.device}")

            counts = sim.run(qasm, shots=1)

            if counts:
                processor = PeakedCircuitProcessor(use_exact=False)
                result = processor.process(counts)
                peak_bitstring = result.get("peak_bitstring")

                if peak_bitstring:
                    bt.logging.info("MPS simulation successful")
                    return peak_bitstring

            return ""

        except Exception as e:
            bt.logging.debug(f"MPS simulation failed: {e}")
            return ""

    def _run(self, qasm: str) -> str:
        """Legacy method - use _run_statevector instead."""
        return self._run_statevector(qasm, prefer_gpu=True)

    def _run_statevector(self, qasm: str, prefer_gpu: bool = True) -> str:
        """Run statevector simulation with device preference."""
        devices = ["GPU", "CPU"] if prefer_gpu else ["CPU", "GPU"]

        for device in devices:
            try:
                sim = create_simulator("qiskit", method="statevector", device=device)
                bt.logging.debug(f"Attempting statevector simulation on device: {sim.device}")

                statevector = sim.get_statevector(qasm)

                if statevector is not None:
                    processor = PeakedCircuitProcessor(use_exact=True)
                    result = processor.process(statevector)
                    peak_bitstring = result.get("peak_bitstring")

                    if peak_bitstring:
                        bt.logging.info(f"Statevector simulation successful on {device}")
                        return peak_bitstring

            except Exception as e:
                bt.logging.debug(f"Simulation failed on {device}: {e}")
                if device == "CPU" or not prefer_gpu:
                    bt.logging.error(f"Statevector simulation failed on all devices: {e}")
                    self._clear_memory()
                continue

        return ""

    def _sampling_run(self, qasm: str, shots: int = 8192) -> str:
        """Run sampling simulation for very large circuits."""
        try:
            sim = create_simulator("qiskit", method="automatic", device="CPU")
            bt.logging.debug(f"Attempting sampling simulation with {shots} shots")

            counts = sim.run(qasm, shots=shots)

            if counts:
                processor = PeakedCircuitProcessor(use_exact=False)
                result = processor.process(counts)
                peak_bitstring = result.get("peak_bitstring")

                if peak_bitstring:
                    bt.logging.info(f"Sampling simulation successful with {shots} shots")
                    return peak_bitstring

            return ""

        except Exception as e:
            bt.logging.debug(f"Sampling simulation failed: {e}")
            return ""

    def _count_qubits(self, qasm: str) -> int:
        import re

        for line in qasm.split("\n"):
            if line.strip().startswith("qreg"):
                match = re.search(r"qreg\s+\w+\[(\d+)\]", line)
                if match:
                    return int(match.group(1))
        raise ValueError("Could not determine number of qubits from QASM")

    def _clear_memory(self):
        """Clear memory using the memory manager."""
        try:
            self.memory_manager.cleanup_if_needed(force=True)
        except Exception as e:
            bt.logging.debug(f"Memory cleanup failed: {e}")
            # Fallback to basic cleanup
            try:
                gc.collect()
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
            except Exception:
                pass
