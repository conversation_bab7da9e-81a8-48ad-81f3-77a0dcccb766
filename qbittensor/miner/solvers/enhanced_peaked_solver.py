"""
Enhanced peaked solver with intelligent strategy selection and performance optimization.
"""
import time
from typing import Optional

import bittensor as bt

from ..simulator import create_simulator
from ..task_processors import PeakedCircuitProcessor
from ..utils import (
    get_memory_manager, get_circuit_analyzer, get_circuit_cache,
    StrategySelector, SolverStrategy
)


class EnhancedPeakedSolver:
    """
    Enhanced solver with intelligent strategy selection, caching, and performance optimization.
    
    Features:
    - Circuit analysis and caching
    - Intelligent strategy selection based on circuit characteristics
    - Performance tracking and adaptive optimization
    - Advanced memory management
    - Multiple fallback strategies
    """
    
    def __init__(self):
        self.memory_manager = get_memory_manager()
        self.circuit_analyzer = get_circuit_analyzer()
        self.circuit_cache = get_circuit_cache()
        self.strategy_selector = StrategySelector(self.memory_manager)
        
        # Performance tracking
        self.solve_count = 0
        self.total_solve_time = 0.0
        self.cache_hits = 0
        
        bt.logging.info("Enhanced peaked solver initialized with intelligent strategy selection")
    
    def solve(self, qasm: str) -> str:
        """
        Solve a quantum circuit using intelligent strategy selection.
        
        Args:
            qasm: QASM string of the circuit
            
        Returns:
            Most probable bitstring, or empty string if failed
        """
        start_time = time.time()
        self.solve_count += 1
        
        try:
            # Check cache first
            circuit_hash = self.circuit_analyzer.get_circuit_hash(qasm)
            cached_result = self.circuit_cache.get(circuit_hash)
            if cached_result:
                self.cache_hits += 1
                bt.logging.info(f"Cache hit for circuit {circuit_hash[:10]} (hit rate: {self.cache_hits/self.solve_count:.2%})")
                return cached_result
            
            # Analyze circuit characteristics
            characteristics = self.circuit_analyzer.analyze(qasm)
            
            bt.logging.info(
                f"Solving circuit #{self.solve_count}: {characteristics.num_qubits} qubits, "
                f"{characteristics.num_gates} gates, depth {characteristics.depth}, "
                f"complexity {characteristics.complexity_score:.2e}"
            )
            
            # Select strategies
            strategies = self.strategy_selector.select_strategy(characteristics)
            
            if not strategies:
                bt.logging.error("No feasible strategies found for circuit")
                return ""
            
            # Try strategies in order of preference
            result = self._try_strategies(qasm, strategies, characteristics)
            
            # Cache successful result
            if result:
                self.circuit_cache.put(circuit_hash, result)
                solve_time = time.time() - start_time
                self.total_solve_time += solve_time
                
                bt.logging.info(
                    f"Circuit solved successfully in {solve_time:.2f}s "
                    f"(avg: {self.total_solve_time/self.solve_count:.2f}s)"
                )
            else:
                bt.logging.error("All strategies failed to solve circuit")
            
            return result
            
        except Exception as e:
            bt.logging.error(f"Circuit solving failed: {e}")
            return ""
        finally:
            # Cleanup memory
            self.memory_manager.cleanup_if_needed()
    
    def _try_strategies(self, qasm: str, strategies: list, characteristics) -> str:
        """Try strategies in order until one succeeds."""
        for i, strategy in enumerate(strategies):
            bt.logging.debug(f"Trying strategy {i+1}/{len(strategies)}: {strategy.name}")
            
            strategy_start = time.time()
            success = False
            result = ""
            
            try:
                result = self._execute_strategy(qasm, strategy)
                success = bool(result)
                
                if success:
                    strategy_time = time.time() - strategy_start
                    bt.logging.info(f"Strategy '{strategy.name}' succeeded in {strategy_time:.2f}s")
                    
                    # Record performance
                    self.strategy_selector.record_performance(
                        strategy.name, strategy_time, True, characteristics
                    )
                    
                    return result
                else:
                    bt.logging.debug(f"Strategy '{strategy.name}' failed to produce result")
                    
            except Exception as e:
                bt.logging.debug(f"Strategy '{strategy.name}' failed with error: {e}")
            
            # Record failure
            strategy_time = time.time() - strategy_start
            self.strategy_selector.record_performance(
                strategy.name, strategy_time, success, characteristics
            )
            
            # Clear memory between attempts
            self.memory_manager.cleanup_if_needed()
        
        return ""
    
    def _execute_strategy(self, qasm: str, strategy: SolverStrategy) -> str:
        """Execute a specific solving strategy."""
        if strategy.method == "statevector":
            return self._run_statevector(qasm, strategy.device)
        elif strategy.method == "matrix_product_state":
            return self._run_mps(qasm)
        elif strategy.method == "automatic":
            if strategy.shots:
                return self._run_sampling(qasm, strategy.shots)
            else:
                return self._run_automatic(qasm, strategy.device)
        else:
            raise ValueError(f"Unknown strategy method: {strategy.method}")
    
    def _run_statevector(self, qasm: str, device: str) -> str:
        """Run statevector simulation."""
        sim = create_simulator("qiskit", method="statevector", device=device)
        bt.logging.debug(f"Running statevector simulation on {sim.device}")
        
        statevector = sim.get_statevector(qasm)
        
        if statevector is not None:
            processor = PeakedCircuitProcessor(use_exact=True)
            result = processor.process(statevector)
            return result.get("peak_bitstring", "")
        
        return ""
    
    def _run_mps(self, qasm: str) -> str:
        """Run matrix product state simulation."""
        sim = create_simulator("qiskit", method="matrix_product_state", device="CPU")
        bt.logging.debug("Running MPS simulation")
        
        counts = sim.run(qasm, shots=1)
        
        if counts:
            processor = PeakedCircuitProcessor(use_exact=False)
            result = processor.process(counts)
            return result.get("peak_bitstring", "")
        
        return ""
    
    def _run_sampling(self, qasm: str, shots: int) -> str:
        """Run sampling simulation."""
        sim = create_simulator("qiskit", method="automatic", device="CPU")
        bt.logging.debug(f"Running sampling simulation with {shots} shots")
        
        counts = sim.run(qasm, shots=shots)
        
        if counts:
            processor = PeakedCircuitProcessor(use_exact=False)
            result = processor.process(counts)
            return result.get("peak_bitstring", "")
        
        return ""
    
    def _run_automatic(self, qasm: str, device: str) -> str:
        """Run automatic simulation method."""
        sim = create_simulator("qiskit", method="automatic", device=device)
        bt.logging.debug(f"Running automatic simulation on {sim.device}")
        
        # Try statevector first
        try:
            statevector = sim.get_statevector(qasm)
            if statevector is not None:
                processor = PeakedCircuitProcessor(use_exact=True)
                result = processor.process(statevector)
                return result.get("peak_bitstring", "")
        except:
            pass
        
        # Fallback to sampling
        counts = sim.run(qasm, shots=4096)
        if counts:
            processor = PeakedCircuitProcessor(use_exact=False)
            result = processor.process(counts)
            return result.get("peak_bitstring", "")
        
        return ""
    
    def get_performance_stats(self) -> dict:
        """Get comprehensive performance statistics."""
        cache_hit_rate = self.cache_hits / max(self.solve_count, 1)
        avg_solve_time = self.total_solve_time / max(self.solve_count, 1)
        
        stats = {
            'solver_stats': {
                'total_solves': self.solve_count,
                'cache_hits': self.cache_hits,
                'cache_hit_rate': cache_hit_rate,
                'avg_solve_time': avg_solve_time,
                'total_solve_time': self.total_solve_time,
            },
            'strategy_stats': self.strategy_selector.get_performance_stats(),
            'memory_stats': self.memory_manager.get_memory_info(),
            'cache_stats': self.circuit_cache.get_stats(),
        }
        
        return stats
    
    def reset_stats(self):
        """Reset performance statistics."""
        self.solve_count = 0
        self.total_solve_time = 0.0
        self.cache_hits = 0
        self.memory_manager.reset_peak_stats()
        bt.logging.info("Performance statistics reset")
    
    def optimize_for_workload(self, expected_circuit_sizes: list, expected_complexity: str = "medium"):
        """Optimize solver settings for expected workload."""
        bt.logging.info(f"Optimizing for workload: sizes {expected_circuit_sizes}, complexity {expected_complexity}")
        
        # Adjust cache size based on expected workload
        if expected_complexity == "high" or max(expected_circuit_sizes) > 30:
            # Larger cache for complex circuits
            self.circuit_cache._max_size = min(2000, self.circuit_cache._max_size * 2)
        
        # Pre-warm memory management for expected sizes
        for size in expected_circuit_sizes:
            self.memory_manager.optimize_for_circuit_size(size)
        
        bt.logging.info("Solver optimization completed")
