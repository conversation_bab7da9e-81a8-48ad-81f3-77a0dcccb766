{"cells": [{"cell_type": "markdown", "id": "043ade73", "metadata": {}, "source": ["## Gaining concentration from random quantum circuits\n", "\n", "The question we are trying to address: Starting with poly(n)-sized random quantum circuits (RQC), how many additional gates do we need to get concentration on some bit string? We numerically address the question here by optimizing over paramitrized quantum circuits (PQC)\n", "\n", "The objective function is:\n", "$$\\max_{{\\pmb\\theta}} |\\langle{0^n|U_{rqc}U_{pqc}(\\pmb\\theta)}|{0^n}\\rangle|^2. $$\n", "\n", "We first start from generating a depth-n random circuit with brickwall structure, acted on the all-zero state:"]}, {"cell_type": "code", "execution_count": 1, "id": "4c5096d6", "metadata": {}, "outputs": [], "source": ["from circuits import*\n", "\n", "n = 12 # system size\n", "depth = n # circuit depth\n", "rqc = qmps_f(n, in_depth=depth, n_Qbit=n-1, qmps_structure=\"brickwall\",)\n"]}, {"cell_type": "markdown", "id": "8b5df636", "metadata": {}, "source": ["We could also draw the circuit as a tensornetwork, the arrow indicates the direction of computation:"]}, {"cell_type": "code", "execution_count": 2, "id": "b163cbc2", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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\n", "text/plain": ["<Figure size 432x432 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rqc.draw()"]}, {"cell_type": "markdown", "id": "103e7cf4", "metadata": {}, "source": ["Then define a PQC with half of the RQC depth:"]}, {"cell_type": "code", "execution_count": 3, "id": "f327b6d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["-0.363060408952 [best: -0.363060408952] : : 558it [06:16,  1.48it/s]                       \n"]}], "source": ["pqc = qmps_f(n, in_depth=depth//2, n_Qbit=n-1, qmps_structure=\"brickwall\",)\n", "\n", "optmzr = TNOptimizer(\n", "    pqc,                                # our initial input, the tensors of which to optimize\n", "    loss_fn=negative_overlap,\n", "    norm_fn=norm_f,\n", "    loss_target=-1+1e-2,\n", "    constant_tags=['MPS'],\n", "    loss_constants={'target': rqc},  # this is a constant TN to supply to loss_fn\n", "    autodiff_backend='tensorflow',      # {'jax', 'tensorflow', 'autograd'}\n", "    optimizer='L-BFGS-B',               # supplied to scipy.minimize\n", ")\n", "mps_opt = optmzr.optimize(500,tol = 1e-10,)  # perform ~100 gradient descent steps'''"]}, {"cell_type": "markdown", "id": "e6d99d2f", "metadata": {}, "source": ["Compare this with the average peak weight from depth n/2 RQC (averaged over 100 events):"]}, {"cell_type": "code", "execution_count": 5, "id": "4e8a7bc4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.005950453401162744, 0.00020977040532438418, 0.014530881813522014)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["average_peak_weight(n,depth = n//2, shots=100)[0]\n", "# returns: np.mean(peak), np.std(peak)/np.sqrt(shots), np.max(peak) where peak is the list of the 100 'peaks'"]}, {"cell_type": "markdown", "id": "e0425a85", "metadata": {}, "source": ["This means, by optimizing the PQC, we are gaining much better concentration compared with trivially inverting the last n/2 layers. \n", "\n", "Meanwhile we could also examine the individual gates from the optimized PQC:"]}, {"cell_type": "code", "execution_count": 12, "id": "7c7545da", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 1.+0.j,  0.-0.j, -0.-0.j,  0.-0.j],\n", "       [ 0.+0.j,  1.+0.j,  0.-0.j,  0.-0.j],\n", "       [-0.+0.j,  0.+0.j,  1.+0.j,  0.-0.j],\n", "       [ 0.+0.j,  0.+0.j,  0.+0.j,  1.+0.j]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["U = pqc.tensors[13].data.reshape(4,4)\n", "#Check unitarity:\n", "(<EMAIL>(U.T)).round(10)"]}, {"cell_type": "code", "execution_count": 16, "id": "6479ff51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-0.5666+0.0359j -0.0531+0.1163j -0.4808+0.5028j -0.386 -0.1682j]\n", " [-0.4044-0.1474j  0.568 -0.3259j  0.4541-0.1081j -0.134 -0.3874j]\n", " [-0.113 +0.2215j -0.6008+0.0508j  0.3437+0.1113j  0.2674-0.6105j]\n", " [ 0.1758+0.6322j  0.4373-0.0136j -0.4051-0.0592j  0.3109-0.3373j]]\n"]}], "source": ["print(U.round(4))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}