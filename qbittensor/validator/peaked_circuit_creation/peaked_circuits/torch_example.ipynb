{"cells": [{"cell_type": "code", "execution_count": 8, "id": "c895febc-7742-4b46-bc8f-cd192c52e1ed", "metadata": {}, "outputs": [], "source": ["##@YZ April 2024\n", "import quimb.tensor as qtn\n", "import quimb as qu\n", "from functions import*\n", "import torch\n", "import cotengra as ctg\n"]}, {"cell_type": "code", "execution_count": 5, "id": "44a1bd22-873c-400c-a10b-68ddcc0fb985", "metadata": {}, "outputs": [], "source": ["def norm_fn(psi):\n", "    # parametrize our tensors as isometric/unitary\n", "    return psi.isometrize(method='cayley')\n", "\n", "def loss_fn(psi):\n", "    # compute the total energy, here quimb handles constructing \n", "    # and contracting all the appropriate lightcones \n", "    return - abs((psi_tar.H & psi).contract(all, optimize=opti)) ** 2\n", "\n", "class TNModel(torch.nn.Module):\n", "\n", "    def __init__(self, tn):\n", "        super().__init__()\n", "        # extract the raw arrays and a skeleton of the TN\n", "        params, self.skeleton = qtn.pack(tn)\n", "        # n.b. you might want to do extra processing here to e.g. store each\n", "        # parameter as a reshaped matrix (from left_inds -> right_inds), for \n", "        # some optimizers, and for some torch parametrizations\n", "        self.torch_params = torch.nn.ParameterDict({\n", "            # torch requires strings as keys\n", "            str(i): torch.nn.Parameter(initial)\n", "            for i, initial in params.items()\n", "        })\n", "\n", "    def forward(self):\n", "        # convert back to original int key format\n", "        params = {int(i): p for i, p in self.torch_params.items()}\n", "        # reconstruct the TN with the new parameters\n", "        psi = qtn.unpack(params, self.skeleton)\n", "        # isometrize and then return the energy\n", "        return loss_fn(norm_fn(psi))\n", "\n", "#the following is an optimizer for speeding up tensor network contractions\n", "opti = ctg.ReusableHyperOptimizer(\n", "    progbar=True,\n", "    methods=['greedy'],\n", "    reconf_opts={},\n", "    max_repeats=36,\n", "    optlib='random',\n", "    # directory=  # set this for persistent cache\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "c948be8c-cf3d-46b5-bc7d-cd9382a4f1ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|                                                    | 0/36 [00:00<?, ?it/s]/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: User<PERSON>arning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "/opt/anaconda3/envs/py312/lib/python3.11/site-packages/cotengra/hyperoptimizers/hyper.py:33: UserWarning: Couldn't import `kahypar` - skipping from default hyper optimizer and using basic `labels` method instead.\n", "  warnings.warn(\n", "F=6.098 C=7.147 S=14 P=15.04: 100%|█████████████| 36/36 [00:06<00:00,  5.29it/s]\n", "[W Copy.cpp:301] Warning: Casting complex values to real discards the imaginary part (function operator())\n", "-0.013222391908128826:  22%|███▌            | 1119/5000 [00:14<00:50, 77.41it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping loss difference is smaller than 1e-10\n", "2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.106 C=7.158 S=14 P=15.04: 100%|█████████████| 36/36 [00:06<00:00,  5.46it/s]\n", "-0.03926272960715059:  50%|████████▌        | 2507/5000 [00:31<00:31, 78.46it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping loss difference is smaller than 1e-10\n", "3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.259 C=7.368 S=14 P=15.04: 100%|█████████████| 36/36 [00:08<00:00,  4.20it/s]\n", "-0.06406975144832122:  88%|██████████████▉  | 4381/5000 [01:11<00:10, 61.23it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping loss difference is smaller than 1e-10\n", "4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.268 C=7.381 S=14 P=15.59: 100%|█████████████| 36/36 [00:08<00:00,  4.46it/s]\n", "-0.12645402710789197:  78%|█████████████▎   | 3921/5000 [01:06<00:18, 58.71it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping loss difference is smaller than 1e-10\n", "5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.426 C=7.6 S=14 P=15.05: 100%|███████████████| 36/36 [00:07<00:00,  4.57it/s]\n", "-0.1622811700338676:  74%|█████████████▎    | 3704/5000 [00:58<00:20, 63.14it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Early stopping loss difference is smaller than 1e-10\n", "6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.393 C=7.54 S=14 P=15.05: 100%|██████████████| 36/36 [00:07<00:00,  4.73it/s]\n", "-0.21125544481616435: 100%|█████████████████| 5000/5000 [02:16<00:00, 36.58it/s]\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:21: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:22: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "/var/folders/xq/n67s1gcd4h3_ddhyjq8gjt_9pymrz4/T/ipykernel_5134/2028719282.py:23: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["F=6.511 C=7.646 S=14 P=15.06: 100%|█████████████| 36/36 [00:08<00:00,  4.22it/s]\n", "-0.2410062028134099: 100%|██████████████████| 5000/5000 [01:53<00:00, 44.09it/s]\n"]}], "source": ["L = 14\n", "in_depth = L #RQC depth\n", "psi_2 = qmps_f(L, in_depth=in_depth, n_Qbit=L-1, qmps_structure=\"brickwall\", canon=\"left\")\n", "depth_initial,depth_final,depth_step = 1,L//2+1,1 # PQC depth\n", "peak_wights = []\n", "\n", "# here we use a sequential optimization scheme; namely we gradually add PQC layers and use the previous optimization results as an intialization \n", "for depth in range(depth_initial,depth_final,depth_step):\n", "    psi_pqc = qmps_f(L, in_depth= depth, n_Qbit=L-1, qmps_structure=\"brickwall\", canon=\"left\",start_layer = (in_depth)%2,rand = True)\n", "    psi = psi_pqc.tensors[L]\n", "    \n", "    # here we seperate the 'all-zero state' to the PQC circuit as we don't want to optimize over that\n", "    for i in range (L+1,len(psi_pqc.tensors)):\n", "        psi = psi&psi_pqc.tensors[i]\n", "    if depth != depth_initial:\n", "        psi_c = psi.copy()\n", "        psi = load_para(psi_c, dictionary)\n", "    \n", "    psi_tar = psi_2.copy()\n", "    for i in range (L):\n", "        psi_tar = psi_tar&psi_pqc.tensors[i] \n", "    psi.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "    psi_tar.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "    psi_2.apply_to_arrays(lambda x: torch.tensor(x, dtype=torch.complex128))\n", "    \n", "    model = TNModel(psi)\n", "    model()\n", "    import warnings\n", "    from torch import optim\n", "    with warnings.catch_warnings():\n", "        warnings.filterwarnings(\n", "            action='ignore',\n", "            message='.*trace might not generalize.*',\n", "        )\n", "        model = torch.jit.trace_module(model, {\"forward\": []})\n", "        \n", "    import torch_optimizer\n", "    import tqdm\n", "    \n", "    optimizer = optim.<PERSON>(model.parameters(), lr=.001)\n", "    \n", "    \n", "    its = 5000\n", "    pbar = tqdm.tqdm(range(its),disable=False)\n", "    scheduler = torch.optim.lr_scheduler.StepLR(optimizer,step_size=300, gamma=0.5)\n", "    previous_loss = torch.inf\n", "    for step in pbar:\n", "        show_progress_bar=True\n", "        optimizer.zero_grad()\n", "        loss = model()\n", "        loss.backward()\n", "        def closure():\n", "            return loss\n", "        optimizer.step()\n", "        pbar.set_description(f\"{loss}\")\n", "        progress_bar_refresh_rate=0\n", "        if step > 100 and torch.abs(previous_loss - loss) < 1e-10:\n", "            print(\"Early stopping loss difference is smaller than 1e-10\")\n", "            break\n", "        previous_loss = loss.clone()\n", "    dictionary = save_para(psi)\n", "    peak_wights.append(loss_fn(norm_fn(psi)))"]}, {"cell_type": "code", "execution_count": 7, "id": "1d0b3fbc-9d33-42b6-8875-a211bf0a4d1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[tensor(-0.0132, dtype=torch.float64),\n", " tensor(-0.0393, dtype=torch.float64),\n", " tensor(-0.0641, dtype=torch.float64),\n", " tensor(-0.1265, dtype=torch.float64),\n", " tensor(-0.1623, dtype=torch.float64),\n", " tensor(-0.2113, dtype=torch.float64),\n", " tensor(-0.2410, dtype=torch.float64)]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["peak_wights"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}